# 本地化库文件说明

## 概述
本项目已将所有外部CDN资源下载到本地，以避免加载缓慢的问题。

## 已本地化的资源

### 1. Font Awesome 图标库
- **原始CDN**: `https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css`
- **本地文件**:
  - `font-awesome-local.css` - 本地化CSS文件，包含必要的图标类
  - `fa-solid-900.woff2` - 实心图标字体文件
  - `fa-regular-400.woff2` - 常规图标字体文件

### 2. ECharts 图表库
- **原始CDN**: `https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js`
- **本地文件**: `echarts.min.js`

### 3. MQTT.js 通信库
- **原始CDN**: `https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js`
- **本地文件**: `mqtt.min.js`

### 4. SheetJS (XLSX) Excel处理库
- **原始CDN**: `https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js`
- **本地文件**: `xlsx.full.min.js`

## 使用方法

所有HTML文件中的CDN链接已替换为本地路径：
- 图标库：`<link rel="stylesheet" href="./libs/font-awesome-local.css">`
- ECharts：`<script src="./libs/echarts.min.js"></script>`
- MQTT：`<script src="./libs/mqtt.min.js"></script>`
- XLSX：`<script src="./libs/xlsx.full.min.js"></script>`

## 更新日期
2025年08月18日