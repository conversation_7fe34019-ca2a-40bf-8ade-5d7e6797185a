<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>桂林智源 SVG 数字化系统 - 水冷系统拓扑图</title>
    <link rel="stylesheet" href="./libs/font-awesome-local.css">
    <style>
        /**
         * 桂林智源 SVG 数字化系统 - 水冷系统拓扑图页面样式
         * 深色科技主题，工业监控界面风格
         */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #00d4ff;
            --secondary-color: #0099cc;
            --accent-color: #00ff88;
            --warning-color: #ffaa00;
            --error-color: #ff4444;
            --success-color: #00ff88;
            --bg-primary: #0a0f1c;
            --bg-secondary: #1a2332;
            --bg-tertiary: #2a3441;
            --text-primary: #ffffff;
            --text-secondary: #b8c5d6;
            --border-color: #3a4a5c;
            --shadow-color: rgba(0, 212, 255, 0.3);
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            width: 1366px;
            height: 768px;
            overflow: hidden;
            position: relative;
        }

        /* 背景动画效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 255, 136, 0.1) 0%, transparent 50%);
            animation: backgroundPulse 4s ease-in-out infinite alternate;
            pointer-events: none;
        }

        @keyframes backgroundPulse {
            0% {
                opacity: 0.3;
            }

            100% {
                opacity: 0.6;
            }
        }

        .container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 1;
        }

        /* 页面头部 */
        .page-header {
            background: linear-gradient(90deg, var(--bg-secondary), var(--bg-tertiary));
            border-bottom: 2px solid var(--primary-color);
            padding: 15px 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0, 212, 255, 0.3);
        }

        .page-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .page-title i {
            font-size: 28px;
            color: var(--accent-color);
        }

        .current-time {
            font-size: 16px;
            color: var(--text-secondary);
            font-family: 'Consolas', monospace;
        }

        /* 主要内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            gap: 20px;
        }

        /* 拓扑图容器 */
        .topology-container {
            flex: 1;
            background: var(--bg-secondary);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .topology-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            border-radius: 12px 12px 0 0;
        }

        .topology-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .topology-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .topology-status {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: var(--bg-tertiary);
            border-radius: 20px;
            font-size: 14px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: statusPulse 2s ease-in-out infinite;
        }

        @keyframes statusPulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }
        }

        /* 拓扑图显示区域 */
        .topology-display {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .topology-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 8px;
            background: var(--bg-primary);
        }

        /* 控制面板 */
        .control-panel {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 15px;
            padding: 15px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .control-buttons {
            display: flex;
            gap: 10px;
        }

        .control-btn {
            padding: 8px 16px;
            background: var(--bg-secondary);
            border: 1px solid var(--primary-color);
            border-radius: 6px;
            color: var(--primary-color);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .control-btn:hover {
            background: var(--primary-color);
            color: var(--bg-primary);
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.4);
        }

        .connection-info {
            color: var(--text-secondary);
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .connection-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: connectionPulse 2s ease-in-out infinite;
        }

        @keyframes connectionPulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }
        }

        /* 响应式设计 */
        @media (max-width: 1366px) {
            body {
                width: 100vw;
                height: 100vh;
            }

            .page-header {
                padding: 12px 20px;
            }

            .main-content {
                padding: 15px;
            }
        }

        /* 加载动画 */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: 15px;
            color: var(--text-secondary);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 页面头部 -->
        <header class="page-header" style="display: none;">
            <div class="page-title">
                <i class="fas fa-tint"></i>
                <span>水冷系统拓扑图</span>
            </div>
            <div class="current-time" id="currentTime">
                <!-- 当前时间将通过JavaScript动态更新 -->
            </div>
        </header>

        <!-- 主要内容区域 -->
        <iframe id="topologyIframe" class="topology-iframe"
            src="https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=29bd5260-38c5-4b4f-97a0-66e97903f9e9"
            style="display: none;">
        </iframe>
    </div>

    <script>
        /**
         * 桂林智源 SVG 数字化系统 - 水冷系统拓扑图页面脚本
         * 处理拓扑图显示、交互和实时更新功能
         */

        // 全局变量
        let topologyIframe = null;
        let isLoaded = false;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            console.log('水冷系统拓扑图页面初始化开始...');
            initTopologyPage();
            updateTime();
            setInterval(updateTime, 1000);
            console.log('水冷系统拓扑图页面初始化完成');
        });

        /**
         * 初始化拓扑图页面
         */
        function initTopologyPage() {
            topologyIframe = document.getElementById('topologyIframe');
            initIframeEvents();

            // 延迟显示iframe以确保加载
            setTimeout(() => {
                showTopologyIframe();
            }, 2000);
        }

        /**
         * 更新时间显示
         */
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        /**
         * 初始化iframe事件
         */
        function initIframeEvents() {
            if (topologyIframe) {
                topologyIframe.onload = function () {
                    console.log('水冷系统拓扑图加载完成');
                    isLoaded = true;
                    hideLoadingIndicator();
                };

                topologyIframe.onerror = function () {
                    console.error('水冷系统拓扑图加载失败');
                    showErrorMessage();
                };
            }
        }

        /**
         * 显示拓扑图iframe
         */
        function showTopologyIframe() {
            const loading = document.getElementById('loadingIndicator');

            if (topologyIframe) {
                topologyIframe.style.display = 'block';

                // 如果2秒后还没加载完成，也隐藏加载指示器
                setTimeout(() => {
                    if (!isLoaded) {
                        hideLoadingIndicator();
                    }
                }, 3000);
            }
        }

        /**
         * 隐藏加载指示器
         */
        function hideLoadingIndicator() {
            const loading = document.getElementById('loadingIndicator');
            if (loading) {
                loading.style.display = 'none';
            }
        }

        /**
         * 显示错误消息
         */
        function showErrorMessage() {
            const loading = document.getElementById('loadingIndicator');
            if (loading) {
                loading.innerHTML = `
                    <i class="fas fa-exclamation-triangle" style="font-size: 24px; color: var(--warning-color);"></i>
                    <span>水冷系统连接失败，请检查网络连接</span>
                `;
            }
        }

        /**
         * 刷新拓扑图
         */
        function refreshTopology() {
            const loading = document.getElementById('loadingIndicator');

            // 显示加载状态
            loading.style.display = 'flex';
            loading.innerHTML = `
                <div class="loading-spinner"></div>
                <span>正在刷新水冷系统...</span>
            `;

            if (topologyIframe) {
                topologyIframe.style.display = 'none';
                isLoaded = false;

                // 重新加载iframe
                const currentSrc = topologyIframe.src;
                topologyIframe.src = '';
                setTimeout(() => {
                    topologyIframe.src = currentSrc;
                    showTopologyIframe();
                }, 500);
            }

            console.log('刷新水冷系统拓扑图');
        }

        /**
         * 全屏显示
         */
        function fullScreen() {
            const element = document.getElementById('topologyDisplay');
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
            console.log('切换到全屏显示');
        }

        /**
         * 在新标签页中打开
         */
        function openInNewTab() {
            const url = 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=29bd5260-38c5-4b4f-97a0-66e97903f9e9';
            window.open(url, '_blank');
            console.log('在新标签页中打开水冷系统拓扑图');
        }
    </script>
</body>

</html>